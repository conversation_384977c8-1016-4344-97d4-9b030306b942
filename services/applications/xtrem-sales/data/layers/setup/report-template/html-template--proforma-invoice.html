<section
  class="record-context"
  data-context-object-type="SalesOrder"
  data-context-object-path="xtremSales.salesOrder.query.edges.0.node"
  data-context-filter='[{"_id":"1","label":"_id","filterType":"matches","filterValue":"salesOrder","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"id":"_id","labelPath":"_id","property":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv","id":"_id","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"key":"_id","labelKey":"_id","labelPath":"_id"},"parameter":true}]'
  data-context-list-order="{}"
  data-alias="HzaiCSRh"
>
  <!--{{#with HzaiCSRh.salesOrder.query.edges.0.node}}-->
  <div class="report-context-body">
    <figure class="table" style="width: 100%">
      <table class="ck-table-resized">
        <colgroup>
          <col style="width: 35.14%" />
          <col style="width: 64.86%" />
        </colgroup>
        <tbody>
          <tr>
            <td>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"site.name","key":"site.name","labelKey":"Name","labelPath":"Sales site > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq site.name null ) ( eq site.name "" ) ( eq site.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Name"
                        data-property-data-type="String"
                        data-property-name="site.name"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{site.name}}</span
                      ></strong
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"site.primaryAddress.addressLine1","key":"site.primaryAddress.addressLine1","labelKey":"Address line 1","labelPath":"Sales site > Primary address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq site.primaryAddress.addressLine1 null ) ( eq site.primaryAddress.addressLine1 "" ) ( eq site.primaryAddress.addressLine1 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Address line 1"
                      data-property-data-type="String"
                      data-property-name="site.primaryAddress.addressLine1"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{site.primaryAddress.addressLine1}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"site.primaryAddress.addressLine2","key":"site.primaryAddress.addressLine2","labelKey":"Address line 2","labelPath":"Sales site > Primary address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq site.primaryAddress.addressLine2 null ) ( eq site.primaryAddress.addressLine2 "" ) ( eq site.primaryAddress.addressLine2 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Address line 2"
                      data-property-data-type="String"
                      data-property-name="site.primaryAddress.addressLine2"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{site.primaryAddress.addressLine2}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"site.primaryAddress.city","key":"site.primaryAddress.city","labelKey":"City","labelPath":"Sales site > Primary address > City"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq site.primaryAddress.city null ) ( eq site.primaryAddress.city "" ) ( eq site.primaryAddress.city undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="City"
                      data-property-data-type="String"
                      data-property-name="site.primaryAddress.city"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{site.primaryAddress.city}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"site.primaryAddress.region","key":"site.primaryAddress.region","labelKey":"Region","labelPath":"Sales site > Primary address > Region"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"site.primaryAddress.postcode","key":"site.primaryAddress.postcode","labelKey":"Postal code","labelPath":"Sales site > Primary address > Postal code"},"value2":null,"key":"2","operator":"notEmpty"}]'
              >
                <!--{{#if ( and ( not ( or ( eq site.primaryAddress.region null ) ( eq site.primaryAddress.region "" ) ( eq site.primaryAddress.region undefined ) ) ) ( not ( or ( eq site.primaryAddress.postcode null ) ( eq site.primaryAddress.postcode "" ) ( eq site.primaryAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Region"
                      data-property-data-type="String"
                      data-property-name="site.primaryAddress.region"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{site.primaryAddress.region}}</span
                    ><span
                      class="property"
                      data-property-display-label="Postal code"
                      data-property-data-type="String"
                      data-property-name="site.primaryAddress.postcode"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{site.primaryAddress.postcode}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"site.primaryAddress.country.name","key":"site.primaryAddress.country.name","labelKey":"Name","labelPath":"Sales site > Primary address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq site.primaryAddress.country.name null ) ( eq site.primaryAddress.country.name "" ) ( eq site.primaryAddress.country.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="site.primaryAddress.country.name"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{site.primaryAddress.country.name}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"site.siret","key":"site.siret","labelKey":"SIRET","labelPath":"Sales site > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq site.siret null ) ( eq site.siret "" ) ( eq site.siret undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}&nbsp;</strong
                    ><span
                      class="property"
                      data-property-display-label="SIRET"
                      data-property-data-type="String"
                      data-property-name="site.siret"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{site.siret}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"site.taxIdNumber","key":"site.taxIdNumber","labelKey":"Tax ID number","labelPath":"Sales site > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq site.taxIdNumber null ) ( eq site.taxIdNumber "" ) ( eq site.taxIdNumber undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}</strong>&nbsp;<span
                      class="property"
                      data-property-display-label="Tax ID number"
                      data-property-data-type="String"
                      data-property-name="site.taxIdNumber"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{site.taxIdNumber}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
            </td>
            <td style="background-color: #dfdfdf; text-align: right">
              <h2>
                <span style="color: #198e59"><strong>{{ translatedContent "c3605cc767d94323cb9636c219d9a834" }}</strong></span>
              </h2>
              <p>
                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                  ><strong>{{ translatedContent "d85553e6500e0607ec0a904e579a1eef" }}</strong></span
                ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                  ><strong
                    ><span
                      class="property"
                      data-property-display-label="Number"
                      data-property-data-type="String"
                      data-property-name="number"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{number}}</span
                    ></strong
                  ></span
                >
              </p>
              <section
                class="record-context"
                data-context-object-type="ProformaInvoice"
                data-context-object-path="proformaInvoices.query.edges.0.node"
                data-context-filter="[]"
                data-context-list-order="{}"
                data-alias="LcUhipQb"
              >
                <!--{{#with LcUhipQb.query.edges.0.node}}-->
                <div class="report-context-body">
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "d70966fd32bce976da6b1ea48a4779b9" }}</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        ><span
                          class="property"
                          data-property-display-label="Issue date"
                          data-property-data-type="Date"
                          data-property-name="issueDate"
                          data-property-data-format="FullDate"
                          data-property-parent-context="ProformaInvoice"
                          >{{formatDate issueDate 'FullDate'}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "84e45d926b8fb391c97f4c6e4f41ccf2" }}</strong></span
                    >
                  </p>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "fee965235205e3d6d6bea5645e45f352" }}</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        >&nbsp;<span
                          class="property"
                          data-property-display-label="Expiration date"
                          data-property-data-type="Date"
                          data-property-name="expirationDate"
                          data-property-data-format="FullDate"
                          data-property-parent-context="ProformaInvoice"
                          >{{formatDate expirationDate 'FullDate'}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "b5c082c79ae7092a0a96d3b5123287d8" }}</strong></span
                    >
                  </p>
                </div>
                <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
              </section>
            </td>
          </tr>
          <tr>
            <td style="border-color: transparent">
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"billToCustomer.id","key":"billToCustomer.id","labelKey":"ID","labelPath":"Bill-to customer > ID"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq billToCustomer.id null ) ( eq billToCustomer.id "" ) ( eq billToCustomer.id undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "5eea367ea73b909880393bd1ae79fc67" }}</strong>&nbsp;</span
                  ><span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Name"
                        data-property-data-type="String"
                        data-property-name="billToCustomer.name"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{billToCustomer.name}}</span
                      ></strong
                    ></span
                  ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt">&nbsp;</span>
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"billToCustomer.id","key":"billToCustomer.id","labelKey":"ID","labelPath":"Bill-to customer > ID"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq billToCustomer.id null ) ( eq billToCustomer.id "" ) ( eq billToCustomer.id undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "d13d8380c3f4de07fef91a42fe6c60d7" }}&nbsp;</strong
                    ><span
                      class="property"
                      data-property-display-label="ID"
                      data-property-data-type="String"
                      data-property-name="billToCustomer.id"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{billToCustomer.id}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"billToCustomer.siret","key":"billToCustomer.siret","labelKey":"SIRET","labelPath":"Bill-to customer > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq billToCustomer.siret null ) ( eq billToCustomer.siret "" ) ( eq billToCustomer.siret undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}</strong>&nbsp;<span
                      class="property"
                      data-property-display-label="SIRET"
                      data-property-data-type="String"
                      data-property-name="billToCustomer.siret"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{billToCustomer.siret}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"billToCustomer.taxIdNumber","key":"billToCustomer.taxIdNumber","labelKey":"Tax ID number","labelPath":"Bill-to customer > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq billToCustomer.taxIdNumber null ) ( eq billToCustomer.taxIdNumber "" ) ( eq billToCustomer.taxIdNumber undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}&nbsp;</strong
                    ><span
                      class="property"
                      data-property-display-label="Tax ID number"
                      data-property-data-type="String"
                      data-property-name="billToCustomer.taxIdNumber"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{billToCustomer.taxIdNumber}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
            </td>
            <td style="border-color: transparent; text-align: right">
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Expected delivery date","data":{"name":"expectedDeliveryDate","title":"Expected delivery date","canSort":true,"canFilter":true,"type":"Date","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Expected delivery date","node":"Date","iconType":"csv"},"id":"expectedDeliveryDate","key":"expectedDeliveryDate","labelKey":"Expected delivery date","labelPath":"Expected delivery date"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq expectedDeliveryDate null ) ( eq expectedDeliveryDate "" ) ( eq expectedDeliveryDate undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "5d2465f709d5a1a3e91dd31102058284" }}</strong
                    ><span
                      class="property"
                      data-property-display-label="Expected delivery date"
                      data-property-data-type="Date"
                      data-property-name="expectedDeliveryDate"
                      data-property-data-format="FullDate"
                      data-property-parent-context="SalesOrder"
                      >{{formatDate expectedDeliveryDate 'FullDate'}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"deliveryMode.name","key":"deliveryMode.name","labelKey":"Name","labelPath":"Delivery mode > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq deliveryMode.name null ) ( eq deliveryMode.name "" ) ( eq deliveryMode.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "0f55fedcee89c8d9407351716835247e" }}</strong
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="deliveryMode.name"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{deliveryMode.name}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"incoterm.name","key":"incoterm.name","labelKey":"Name","labelPath":"Incoterms rule > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq incoterm.name null ) ( eq incoterm.name "" ) ( eq incoterm.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "c42d7a1f13e2fd7ab2c829f3662f736f" }}</strong
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="incoterm.name"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{incoterm.name}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
            </td>
          </tr>
        </tbody>
      </table>
    </figure>
    <figure class="table" style="width: 100%">
      <table class="ck-table-resized">
        <colgroup>
          <col style="width: 48.79%" />
          <col style="width: 51.21%" />
        </colgroup>
        <tbody>
          <tr>
            <td style="background-color: #dfdfdf; border-color: #dfdfdf">
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>{{ translatedContent "e5a91ba97f94a8fef20e114808cc9c40" }}</strong></span
              >
            </td>
            <td style="background-color: #dfdfdf; border-color: #dfdfdf">
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>{{ translatedContent "6fd791a834ec2f5a235b58bdea1746fc" }}</strong></span
              >
            </td>
          </tr>
          <tr>
            <td style="border-color: transparent">
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"shipToAddress.name","key":"shipToAddress.name","labelKey":"Name","labelPath":"Ship-to address > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq shipToAddress.name null ) ( eq shipToAddress.name "" ) ( eq shipToAddress.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Name"
                        data-property-data-type="String"
                        data-property-name="shipToAddress.name"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{shipToAddress.name}}</span
                      ></strong
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"shipToAddress.addressLine1","key":"shipToAddress.addressLine1","labelKey":"Address line 1","labelPath":"Ship-to address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq shipToAddress.addressLine1 null ) ( eq shipToAddress.addressLine1 "" ) ( eq shipToAddress.addressLine1 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Address line 1"
                      data-property-data-type="String"
                      data-property-name="shipToAddress.addressLine1"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{shipToAddress.addressLine1}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"shipToAddress.addressLine2","key":"shipToAddress.addressLine2","labelKey":"Address line 2","labelPath":"Ship-to address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq shipToAddress.addressLine2 null ) ( eq shipToAddress.addressLine2 "" ) ( eq shipToAddress.addressLine2 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Address line 2"
                      data-property-data-type="String"
                      data-property-name="shipToAddress.addressLine2"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{shipToAddress.addressLine2}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"shipToAddress.city","key":"shipToAddress.city","labelKey":"City","labelPath":"Ship-to address > City"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq shipToAddress.city null ) ( eq shipToAddress.city "" ) ( eq shipToAddress.city undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="City"
                      data-property-data-type="String"
                      data-property-name="shipToAddress.city"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{shipToAddress.city}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"shipToAddress.region","key":"shipToAddress.region","labelKey":"Region","labelPath":"Ship-to address > Region"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"shipToAddress.postcode","key":"shipToAddress.postcode","labelKey":"Postal code","labelPath":"Ship-to address > Postal code"},"value2":null,"key":"2","operator":"notEmpty"}]'
              >
                <!--{{#if ( and ( not ( or ( eq shipToAddress.region null ) ( eq shipToAddress.region "" ) ( eq shipToAddress.region undefined ) ) ) ( not ( or ( eq shipToAddress.postcode null ) ( eq shipToAddress.postcode "" ) ( eq shipToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Region"
                      data-property-data-type="String"
                      data-property-name="shipToAddress.region"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{shipToAddress.region}}</span
                    ><span
                      class="property"
                      data-property-display-label="Postal code"
                      data-property-data-type="String"
                      data-property-name="shipToAddress.postcode"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{shipToAddress.postcode}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"shipToAddress.country.name","key":"shipToAddress.country.name","labelKey":"Name","labelPath":"Ship-to address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq shipToAddress.country.name null ) ( eq shipToAddress.country.name "" ) ( eq shipToAddress.country.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="shipToAddress.country.name"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{shipToAddress.country.name}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
            </td>
            <td style="border-color: transparent">
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"billToAddress.name","key":"billToAddress.name","labelKey":"Name","labelPath":"Bill-to address > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq billToAddress.name null ) ( eq billToAddress.name "" ) ( eq billToAddress.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Name"
                        data-property-data-type="String"
                        data-property-name="billToAddress.name"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{billToAddress.name}}</span
                      ></strong
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"billToAddress.addressLine1","key":"billToAddress.addressLine1","labelKey":"Address line 1","labelPath":"Bill-to address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq billToAddress.addressLine1 null ) ( eq billToAddress.addressLine1 "" ) ( eq billToAddress.addressLine1 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Address line 1"
                      data-property-data-type="String"
                      data-property-name="billToAddress.addressLine1"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{billToAddress.addressLine1}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"billToAddress.addressLine2","key":"billToAddress.addressLine2","labelKey":"Address line 2","labelPath":"Bill-to address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq billToAddress.addressLine2 null ) ( eq billToAddress.addressLine2 "" ) ( eq billToAddress.addressLine2 undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Address line 2"
                      data-property-data-type="String"
                      data-property-name="billToAddress.addressLine2"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{billToAddress.addressLine2}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"billToAddress.city","key":"billToAddress.city","labelKey":"City","labelPath":"Bill-to address > City"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq billToAddress.city null ) ( eq billToAddress.city "" ) ( eq billToAddress.city undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="City"
                      data-property-data-type="String"
                      data-property-name="billToAddress.city"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{billToAddress.city}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"billToAddress.region","key":"billToAddress.region","labelKey":"Region","labelPath":"Bill-to address > Region"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"billToAddress.postcode","key":"billToAddress.postcode","labelKey":"Postal code","labelPath":"Bill-to address > Postal code"},"value2":null,"key":"2","operator":"notEmpty"}]'
              >
                <!--{{#if ( and ( not ( or ( eq billToAddress.region null ) ( eq billToAddress.region "" ) ( eq billToAddress.region undefined ) ) ) ( not ( or ( eq billToAddress.postcode null ) ( eq billToAddress.postcode "" ) ( eq billToAddress.postcode undefined ) ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Region"
                      data-property-data-type="String"
                      data-property-name="billToAddress.region"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{billToAddress.region}}</span
                    ><span
                      class="property"
                      data-property-display-label="Postal code"
                      data-property-data-type="String"
                      data-property-name="billToAddress.postcode"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{billToAddress.postcode}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
              <section
                class="conditional-block"
                data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"billToAddress.country.name","key":"billToAddress.country.name","labelKey":"Name","labelPath":"Bill-to address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
              >
                <!--{{#if ( not ( or ( eq billToAddress.country.name null ) ( eq billToAddress.country.name "" ) ( eq billToAddress.country.name undefined ) ) )}}-->
                <div class="conditional-block-body">
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="billToAddress.country.name"
                      data-property-data-format=""
                      data-property-parent-context="SalesOrder"
                      >{{billToAddress.country.name}}</span
                    ></span
                  >
                </div>
                <!--{{/if}}-->
                <div class="conditional-block-footer">&nbsp;</div>
              </section>
            </td>
          </tr>
        </tbody>
      </table>
    </figure>
    <table
      class="query-table"
      data-context-object-type="SalesOrderLine"
      data-context-object-path="lines.query.edges"
      data-context-filter="[]"
      data-context-list-order='{"item":{ "Id":"ascending"},"itemDescription":"ascending","quantity":"ascending","unit.name":"ascending","netPrice":"ascending","discount":"ascending","charge":"ascending","amountExcludingTax":"ascending","amountIncludingTax":"ascending"}'
      data-alias="UcIhHaQl"
    >
      <thead class="query-table-head">
        <tr class="query-table-row">
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>&nbsp;{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}&nbsp;</strong></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>&nbsp;{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</strong></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>&nbsp;{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}&nbsp;</strong></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>&nbsp;{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</strong></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>&nbsp;{{ translatedContent "9554ce40e7959ce198210376c97150b3" }}</strong></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>&nbsp;{{ translatedContent "104d9898c04874d0fbac36e125fa1369" }}</strong></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>&nbsp;{{ translatedContent "517349a3cdc1acf50617693e3ba33988" }}</strong></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>&nbsp;{{ translatedContent "c17f9183c57045cfb614e02bbb233ee1" }}</strong></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>&nbsp;{{ translatedContent "41dd1e26b4321ab2f837f69a7aae8e91" }}</strong></span
              >
            </p>
          </td>
        </tr>
      </thead>
      <tbody class="query-table-body">
        <!--{{#each UcIhHaQl.query.edges}}{{#with node}}-->
        <tr class="query-table-row">
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><span
                  class="property"
                  data-property-display-label="Item ID"
                  data-property-data-type="String"
                  data-property-name="item.id"
                  data-property-data-format=""
                  data-property-parent-context="SalesOrderLine"
                  >{{item.id}}</span
                ></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><span
                  class="property"
                  data-property-display-label="Item description"
                  data-property-data-type="String"
                  data-property-name="itemDescription"
                  data-property-data-format=""
                  data-property-parent-context="SalesOrderLine"
                  >{{itemDescription}}</span
                ></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><span
                  class="property"
                  data-property-display-label="Quantity in sales unit"
                  data-property-data-type="Decimal"
                  data-property-name="quantity"
                  data-property-data-format="2"
                  data-property-parent-context="SalesOrderLine"
                  >{{formatNumber quantity 2}}</span
                ></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><span
                  class="property"
                  data-property-display-label="Name"
                  data-property-data-type="String"
                  data-property-name="unit.name"
                  data-property-data-format=""
                  data-property-parent-context="SalesOrderLine"
                  >{{unit.name}}</span
                ></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><span
                  class="property"
                  data-property-display-label="Symbol"
                  data-property-data-type="String"
                  data-property-name="document.transactionCurrency.symbol"
                  data-property-data-format=""
                  data-property-parent-context="SalesOrderLine"
                  >{{document.transactionCurrency.symbol}}</span
                ><span
                  class="property"
                  data-property-display-label="Net price"
                  data-property-data-type="Decimal"
                  data-property-name="netPrice"
                  data-property-data-format="2"
                  data-property-parent-context="SalesOrderLine"
                  >{{formatNumber netPrice 2}}</span
                ></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><span
                  class="property"
                  data-property-display-label="Discount"
                  data-property-data-type="Decimal"
                  data-property-name="discount"
                  data-property-data-format="2"
                  data-property-parent-context="SalesOrderLine"
                  >{{formatNumber discount 2}}</span
                >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
              >
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><span
                  class="property"
                  data-property-display-label="Charge"
                  data-property-data-type="Decimal"
                  data-property-name="charge"
                  data-property-data-format="2"
                  data-property-parent-context="SalesOrderLine"
                  >{{formatNumber charge 2}}</span
                >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
              >
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><span
                  class="property"
                  data-property-display-label="Symbol"
                  data-property-data-type="String"
                  data-property-name="document.transactionCurrency.symbol"
                  data-property-data-format=""
                  data-property-parent-context="SalesOrderLine"
                  >{{document.transactionCurrency.symbol}}</span
                ><span
                  class="property"
                  data-property-display-label="Line amount excluding tax"
                  data-property-data-type="Decimal"
                  data-property-name="amountExcludingTax"
                  data-property-data-format="2"
                  data-property-parent-context="SalesOrderLine"
                  >{{formatNumber amountExcludingTax 2}}</span
                ></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><span
                  class="property"
                  data-property-display-label="Symbol"
                  data-property-data-type="String"
                  data-property-name="document.transactionCurrency.symbol"
                  data-property-data-format=""
                  data-property-parent-context="SalesOrderLine"
                  >{{document.transactionCurrency.symbol}}</span
                ><span
                  class="property"
                  data-property-display-label="Line amount including tax"
                  data-property-data-type="Decimal"
                  data-property-name="amountIncludingTax"
                  data-property-data-format="2"
                  data-property-parent-context="SalesOrderLine"
                  >{{formatNumber amountIncludingTax 2}}</span
                ></span
              >
            </p>
          </td>
        </tr>
        <!--{{#printBreakIfLast  'netPrice' 'sum' 'amountExcludingTax' 'sum' 'amountIncludingTax' 'sum'}}-->
        <tr class="query-table-row" data-footer-group="footer">
          <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px">
            <p>
              <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong>{{ translatedContent "edd291d1439a6c1c18fe38bee411580f" }}</strong></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong
                  ><span
                    class="property"
                    data-property-display-label="Symbol"
                    data-property-data-type="String"
                    data-property-name="document.transactionCurrency.symbol"
                    data-property-data-format=""
                    data-property-parent-context="SalesOrderLine"
                    >{{document.transactionCurrency.symbol}}</span
                  ><span
                    class="property"
                    data-property-display-label="Net price"
                    data-property-data-type="Decimal"
                    data-property-name="_blockAggregatedData.netPrice.sum"
                    data-property-data-format="2"
                    data-property-parent-context="SalesOrderLine"
                    >{{formatNumber _blockAggregatedData.netPrice.sum 2}}</span
                  ></strong
                ></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong
                  ><span
                    class="property"
                    data-property-display-label="Symbol"
                    data-property-data-type="String"
                    data-property-name="document.transactionCurrency.symbol"
                    data-property-data-format=""
                    data-property-parent-context="SalesOrderLine"
                    >{{document.transactionCurrency.symbol}}</span
                  ><span
                    class="property"
                    data-property-display-label="Line amount excluding tax"
                    data-property-data-type="Decimal"
                    data-property-name="_blockAggregatedData.amountExcludingTax.sum"
                    data-property-data-format="2"
                    data-property-parent-context="SalesOrderLine"
                    >{{formatNumber _blockAggregatedData.amountExcludingTax.sum 2}}</span
                  ></strong
                ></span
              >
            </p>
          </td>
          <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
            <p>
              <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                ><strong
                  ><span
                    class="property"
                    data-property-display-label="Symbol"
                    data-property-data-type="String"
                    data-property-name="document.transactionCurrency.symbol"
                    data-property-data-format=""
                    data-property-parent-context="SalesOrderLine"
                    >{{document.transactionCurrency.symbol}}</span
                  ><span
                    class="property"
                    data-property-display-label="Line amount including tax"
                    data-property-data-type="Decimal"
                    data-property-name="_blockAggregatedData.amountIncludingTax.sum"
                    data-property-data-format="2"
                    data-property-parent-context="SalesOrderLine"
                    >{{formatNumber _blockAggregatedData.amountIncludingTax.sum 2}}</span
                  ></strong
                ></span
              >
            </p>
          </td>
        </tr>
        <!--{{/printBreakIfLast}}-->
        <tr class="query-table-row" data-hidden="1">
          <td class="query-table-cell" colspan="9">
            <p>&nbsp;</p>
          </td>
        </tr>
        <tr class="query-table-row" data-hidden="1">
          <td class="query-table-cell" colspan="9">
            <p>&nbsp;</p>
          </td>
        </tr>
        <!--{{/with}}{{/each}}-->
        <tr class="query-table-row" data-hidden="1">
          <td class="query-table-cell" colspan="9">
            <p>&nbsp;</p>
          </td>
        </tr>
      </tbody>
      <tfoot class="query-table-footer">
        <tr class="query-table-row">
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
          <td class="query-table-cell">
            <p>&nbsp;</p>
          </td>
        </tr>
      </tfoot>
    </table>
    <section class="unbreakable-block">
      <div class="unbreakable-block-body">
        <figure class="table">
          <table>
            <tbody>
              <tr>
                <td>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"paymentTerm.name","key":"paymentTerm.name","labelKey":"Name","labelPath":"Payment term > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq paymentTerm.name null ) ( eq paymentTerm.name "" ) ( eq paymentTerm.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="color: #000000; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "6af6aa3c07e3f393b7c213a6a0edd35f" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="paymentTerm.name"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{paymentTerm.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <p>&nbsp;</p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Discount type","data":{"name":"discountType","title":"Discount type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"paymentTerm.discountType","key":"discountType","labelKey":"Discount type","labelPath":"Discount type"},"value2":["percentage"],"key":"1","operator":"set"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Discount amount","data":{"name":"discountAmount","title":"Discount amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.discountAmount","key":"discountAmount","labelKey":"Discount amount","labelPath":"Discount amount"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Discount amount","data":{"name":"discountAmount","title":"Discount amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.discountAmount","key":"discountAmount","labelKey":"Discount amount","labelPath":"Discount amount"},"value2":"0","key":"3","operator":"notEqual"}]'
                  >
                    <!--{{#if ( and (or ( eq paymentTerm.discountType "percentage" )) ( not ( or ( eq paymentTerm.discountAmount null ) ( eq paymentTerm.discountAmount "" ) ( eq paymentTerm.discountAmount undefined ) ) ) ( not ( eq paymentTerm.discountAmount "0" ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Discount amount"
                          data-property-data-type="Decimal"
                          data-property-name="paymentTerm.discountAmount"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrder"
                          >{{formatNumber paymentTerm.discountAmount 2}}&nbsp;</span
                        >{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8z" }}</span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Discount type","data":{"name":"discountType","title":"Discount type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"paymentTerm.discountType","key":"discountType","labelKey":"Discount type","labelPath":"Discount type"},"value2":["amount"],"key":"1","operator":"set"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Discount amount","data":{"name":"discountAmount","title":"Discount amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.discountAmount","key":"discountAmount","labelKey":"Discount amount","labelPath":"Discount amount"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Discount amount","data":{"name":"discountAmount","title":"Discount amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.discountAmount","key":"discountAmount","labelKey":"Discount amount","labelPath":"Discount amount"},"value2":"0","key":"3","operator":"notEqual"}]'
                  >
                    <!--{{#if ( and (or ( eq paymentTerm.discountType "amount" )) ( not ( or ( eq paymentTerm.discountAmount null ) ( eq paymentTerm.discountAmount "" ) ( eq paymentTerm.discountAmount undefined ) ) ) ( not ( eq paymentTerm.discountAmount "0" ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Symbol"
                          data-property-data-type="String"
                          data-property-name="transactionCurrency.symbol"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{transactionCurrency.symbol}}</span
                        ><span
                          class="property"
                          data-property-display-label="Discount amount"
                          data-property-data-type="Decimal"
                          data-property-name="paymentTerm.discountAmount"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrder"
                          >{{formatNumber paymentTerm.discountAmount 2}}&nbsp;</span
                        >{{ translatedContent "54dc979dbdbbf0af917f81c453d0189k" }}</span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Discount type","data":{"name":"paymentTerm.discountType","title":"Discount type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"paymentTerm.discountType","key":"discountType","labelKey":"Discount type","labelPath":"Discount type"},"value2":null,"key":"1","operator":"empty"},{"conjunction":"or","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Discount amount","data":{"name":"discountAmount","title":"Discount amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.discountAmount","key":"discountAmount","labelKey":"Discount amount","labelPath":"Discount amount"},"value2":"0","key":"2","operator":"equals"},{"conjunction":"or","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Discount amount","data":{"name":"discountAmount","title":"Discount amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Discount amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.discountAmount","key":"discountAmount","labelKey":"Discount amount","labelPath":"Discount amount"},"value2":null,"key":"3","operator":"empty"}]'
                  >
                    <!--{{#if ( and (or ( eq paymentTerm.discountType null )) ( or ( eq paymentTerm.discountAmount null ) ( eq paymentTerm.discountAmount "" ) ( eq paymentTerm.discountAmount undefined ) ) ( not ( eq paymentTerm.discountAmount "0" ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        >{{ translatedContent "25a4cef0e6f56a6db3c270c49016e935" }}</span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Penalty type","data":{"name":"penaltyType","title":"Penalty type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"paymentTerm.penaltyType","key":"penaltyType","labelKey":"Penalty type","labelPath":"Penalty type"},"value2":["percentage"],"key":"1","operator":"set"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Penalty amount","data":{"name":"penaltyAmount","title":"Penalty amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.penaltyAmount","key":"penaltyAmount","labelKey":"Penalty amount","labelPath":"Penalty amount"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Penalty amount","data":{"name":"penaltyAmount","title":"Penalty amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.penaltyAmount","key":"penaltyAmount","labelKey":"Penalty amount","labelPath":"Penalty amount"},"value2":"0","key":"3","operator":"notEqual"}]'
                  >
                    <!--{{#if ( and (or ( eq paymentTerm.penaltyType "percentage" )) ( not ( or ( eq paymentTerm.penaltyAmount null ) ( eq paymentTerm.penaltyAmount "" ) ( eq paymentTerm.penaltyAmount undefined ) ) ) ( not ( eq paymentTerm.penaltyAmount "0" ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Penalty amount"
                          data-property-data-type="Decimal"
                          data-property-name="paymentTerm.penaltyAmount"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrder"
                          >{{formatNumber paymentTerm.penaltyAmount 2}}&nbsp;</span
                        >{{ translatedContent "a229634623ed01d6fec61f1d47de427x" }}</span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Penalty type","data":{"name":"penaltyType","title":"Penalty type","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-master-data/DiscountOrPenaltyType","dataType":"discountOrPenaltyTypeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty type","enumValues":["percentage","amount"],"node":"Enum","iconType":"csv"},"id":"paymentTerm.penaltyType","key":"penaltyType","labelKey":"Penalty type","labelPath":"Penalty type"},"value2":["amount"],"key":"1","operator":"set"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Penalty amount","data":{"name":"penaltyAmount","title":"Penalty amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.penaltyAmount","key":"penaltyAmount","labelKey":"Penalty amount","labelPath":"Penalty amount"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Penalty amount","data":{"name":"penaltyAmount","title":"Penalty amount","canSort":true,"canFilter":true,"type":"Decimal","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"priceInSalesPrice","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Penalty amount","node":"Decimal","iconType":"csv"},"id":"paymentTerm.penaltyAmount","key":"penaltyAmount","labelKey":"Penalty amount","labelPath":"Penalty amount"},"value2":"0","key":"3","operator":"notEqual"}]'
                  >
                    <!--{{#if ( and (or ( eq paymentTerm.penaltyType "amount" )) ( not ( or ( eq paymentTerm.penaltyAmount null ) ( eq paymentTerm.penaltyAmount "" ) ( eq paymentTerm.penaltyAmount undefined ) ) ) ( not ( eq paymentTerm.penaltyAmount "0" ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Symbol"
                          data-property-data-type="String"
                          data-property-name="transactionCurrency.symbol"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{transactionCurrency.symbol}}</span
                        ><span
                          class="property"
                          data-property-display-label="Penalty amount"
                          data-property-data-type="Decimal"
                          data-property-name="paymentTerm.penaltyAmount"
                          data-property-data-format="2"
                          data-property-parent-context="SalesOrder"
                          >{{formatNumber paymentTerm.penaltyAmount 2}}&nbsp;</span
                        >{{ translatedContent "e82b32385b756e8476ac5de39323aa0a" }}</span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isStored":true,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"site.legalCompany.legislation.id","key":"site.legalCompany.legislation.id","labelKey":"ID","labelPath":"Site > Legal company > Legislation > ID"},"value2":"FR","key":"1","operator":"equals"}]'
                  >
                    <!--{{#if ( eq site.legalCompany.legislation.id "FR" )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        >{{ translatedContent "213317e9b791d3e46beb61b27650d6cy" }}</span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td>
                  <table
                    class="query-table"
                    data-context-object-type="SalesOrderTax"
                    data-context-object-path="taxes.query.edges"
                    data-context-filter="[]"
                    data-context-list-order='{"tax":"ascending","taxRate":"ascending","taxableAmount":"ascending","taxAmount":"ascending"}'
                    data-alias="XFPQqMhi"
                  >
                    <thead class="query-table-head">
                      <tr class="query-table-row">
                        <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                          <p>
                            <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><strong>{{ translatedContent "4b78ac8eb158840e9638a3aeb26c4a9d" }}</strong></span
                            >
                          </p>
                        </td>
                        <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                          <p>
                            <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><strong>{{ translatedContent "59a99666437cbc6e878c5d4ef3d1c993" }}</strong></span
                            >
                          </p>
                        </td>
                        <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                          <p>
                            <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><strong>{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</strong></span
                            >
                          </p>
                        </td>
                        <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px">
                          <p>
                            <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><strong>{{ translatedContent "617fe2857b30555c036a380e07ce16e3" }}</strong></span
                            >
                          </p>
                        </td>
                      </tr>
                    </thead>
                    <tbody class="query-table-body">
                      <!--{{#each XFPQqMhi.query.edges}}{{#with node}}-->
                      <tr class="query-table-row">
                        <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px">
                          <p>
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><span
                                class="property"
                                data-property-display-label="Tax"
                                data-property-data-type="String"
                                data-property-name="tax"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrderTax"
                                >{{tax}}</span
                              ></span
                            >
                          </p>
                        </td>
                        <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                          <p>
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><span
                                class="property"
                                data-property-display-label="Symbol"
                                data-property-data-type="String"
                                data-property-name="currency.symbol"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrderTax"
                                >{{currency.symbol}}</span
                              ><span
                                class="property"
                                data-property-display-label="Taxable amount"
                                data-property-data-type="Decimal"
                                data-property-name="taxableAmount"
                                data-property-data-format="2"
                                data-property-parent-context="SalesOrderTax"
                                >{{formatNumber taxableAmount 2}}</span
                              ></span
                            >
                          </p>
                        </td>
                        <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                          <p>
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><span
                                class="property"
                                data-property-display-label="Tax rate"
                                data-property-data-type="Decimal"
                                data-property-name="taxRate"
                                data-property-data-format="2"
                                data-property-parent-context="SalesOrderTax"
                                >{{formatNumber taxRate 2}}</span
                              >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
                            >
                          </p>
                        </td>
                        <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                          <p>
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><span
                                class="property"
                                data-property-display-label="Symbol"
                                data-property-data-type="String"
                                data-property-name="currency.symbol"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrderTax"
                                >{{currency.symbol}}</span
                              ><span
                                class="property"
                                data-property-display-label="Tax amount"
                                data-property-data-type="Decimal"
                                data-property-name="taxAmount"
                                data-property-data-format="2"
                                data-property-parent-context="SalesOrderTax"
                                >{{formatNumber taxAmount 2}}</span
                              ></span
                            >
                          </p>
                        </td>
                      </tr>
                      <tr class="query-table-row" data-hidden="1">
                        <td class="query-table-cell" colspan="4">
                          <p>&nbsp;</p>
                        </td>
                      </tr>
                      <!--{{/with}}{{/each}}-->
                      <tr class="query-table-row" data-hidden="1">
                        <td class="query-table-cell" colspan="4">
                          <p>&nbsp;</p>
                        </td>
                      </tr>
                    </tbody>
                    <tfoot class="query-table-footer">
                      <tr class="query-table-row">
                        <td class="query-table-cell">
                          <p>&nbsp;</p>
                        </td>
                        <td class="query-table-cell">
                          <p>&nbsp;</p>
                        </td>
                        <td class="query-table-cell">
                          <p>&nbsp;</p>
                        </td>
                        <td class="query-table-cell">
                          <p>&nbsp;</p>
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                  <figure class="table">
                    <table>
                      <tbody>
                        <tr>
                          <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                            <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><strong>{{ translatedContent "25b77b23fda3d051fb72598c952a82d7" }}</strong></span
                            >
                          </td>
                          <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                            <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><strong>{{ translatedContent "ff9e6ead51b6937f49449b030045d815" }}</strong></span
                            >
                          </td>
                          <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                            <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><strong>{{ translatedContent "ba12e59b20a9720f70fb907a55cd2620" }}</strong></span
                            >
                          </td>
                          <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                            <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><strong>{{ translatedContent "c111a6c864931a3014caed206aff80b5" }}</strong></span
                            >
                          </td>
                        </tr>
                        <tr>
                          <td style="text-align: right">
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><span
                                class="property"
                                data-property-display-label="Symbol"
                                data-property-data-type="String"
                                data-property-name="transactionCurrency.symbol"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrder"
                                >{{transactionCurrency.symbol}}</span
                              ><span
                                class="property"
                                data-property-display-label="Total amount excluding tax"
                                data-property-data-type="Decimal"
                                data-property-name="totalAmountExcludingTax"
                                data-property-data-format="2"
                                data-property-parent-context="SalesOrder"
                                >{{formatNumber totalAmountExcludingTax 2}}</span
                              ></span
                            >
                          </td>
                          <td style="text-align: right">
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><span
                                class="property"
                                data-property-display-label="Symbol"
                                data-property-data-type="String"
                                data-property-name="transactionCurrency.symbol"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrder"
                                >{{transactionCurrency.symbol}}</span
                              ><span
                                class="property"
                                data-property-display-label="Total tax amount"
                                data-property-data-type="Decimal"
                                data-property-name="totalTaxAmount"
                                data-property-data-format="2"
                                data-property-parent-context="SalesOrder"
                                >{{formatNumber totalTaxAmount 2}}</span
                              ></span
                            >
                          </td>
                          <td style="text-align: right">
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><span
                                class="property"
                                data-property-display-label="Symbol"
                                data-property-data-type="String"
                                data-property-name="transactionCurrency.symbol"
                                data-property-data-format=""
                                data-property-parent-context="SalesOrder"
                                >{{transactionCurrency.symbol}}</span
                              ><span
                                class="property"
                                data-property-display-label="Total amount including tax"
                                data-property-data-type="Decimal"
                                data-property-name="totalAmountIncludingTax"
                                data-property-data-format="2"
                                data-property-parent-context="SalesOrder"
                                >{{formatNumber totalAmountIncludingTax 2}}</span
                              ></span
                            >
                          </td>
                          <td style="text-align: right">
                            <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                              ><strong
                                ><span
                                  class="property"
                                  data-property-display-label="Symbol"
                                  data-property-data-type="String"
                                  data-property-name="transactionCurrency.symbol"
                                  data-property-data-format=""
                                  data-property-parent-context="SalesOrder"
                                  >{{transactionCurrency.symbol}}</span
                                ><span
                                  class="property"
                                  data-property-display-label="Total amount including tax"
                                  data-property-data-type="Decimal"
                                  data-property-name="totalAmountIncludingTax"
                                  data-property-data-format="2"
                                  data-property-parent-context="SalesOrder"
                                  >{{formatNumber totalAmountIncludingTax 2}}</span
                                ></strong
                              ></span
                            >
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </figure>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
      </div>
    </section>
    <section class="unbreakable-block">
      <div class="unbreakable-block-body">
        <figure class="table">
          <table>
            <tbody>
              <tr>
                <td style="text-align: center">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Legal form","data":{"name":"legalForm","title":"Legal form","canSort":true,"canFilter":true,"type":"Enum","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":"@sage/xtrem-structure/LegalForm","dataType":"legalFormDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Legal form","enumValues":["SARL","EURL","SELARL","SA","SAS","SASU","SNC","SCP"],"node":"Enum","iconType":"csv"},"id":"site.legalCompany.legalForm","key":"site.legalCompany.legalForm","labelKey":"Legal form","labelPath":"Sales site > Legal company > Legal form"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"site.legalCompany.name","key":"site.legalCompany.name","labelKey":"Name","labelPath":"Sales site > Legal company > Name"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"site.legalCompany.naf","key":"site.legalCompany.naf","labelKey":"NAF","labelPath":"Sales site > Legal company > NAF"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"RCS","data":{"name":"rcs","title":"RCS","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"RCS","node":"String","iconType":"csv"},"id":"site.legalCompany.rcs","key":"site.legalCompany.rcs","labelKey":"RCS","labelPath":"Sales site > Legal company > RCS"},"value2":null,"key":"4","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( not ( or ( eq site.legalCompany.legalForm null ) ( eq site.legalCompany.legalForm "" ) ( eq site.legalCompany.legalForm undefined ) ) ) ( not ( or ( eq site.legalCompany.name null ) ( eq site.legalCompany.name "" ) ( eq site.legalCompany.name undefined ) ) ) ( not ( or ( eq site.legalCompany.naf null ) ( eq site.legalCompany.naf "" ) ( eq site.legalCompany.naf undefined ) ) ) ( not ( or ( eq site.legalCompany.rcs null ) ( eq site.legalCompany.rcs "" ) ( eq site.legalCompany.rcs undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Legal form"
                            data-property-data-type="Enum"
                            data-property-name="site.legalCompany.legalForm"
                            data-property-data-format=""
                            data-property-parent-context="SalesOrder"
                            >{{site.legalCompany.legalForm}}</span
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="site.legalCompany.name"
                            data-property-data-format=""
                            data-property-parent-context="SalesOrder"
                            >{{site.legalCompany.name}}</span
                          ></strong
                        ><span
                          class="property"
                          data-property-display-label="RCS"
                          data-property-data-type="String"
                          data-property-name="site.legalCompany.rcs"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.legalCompany.rcs}}</span
                        >{{ translatedContent "7137a85d9b8d9e6d09f47767d2ca31ee" }}<span
                          class="property"
                          data-property-display-label="NAF"
                          data-property-data-type="String"
                          data-property-name="site.legalCompany.naf"
                          data-property-data-format=""
                          data-property-parent-context="SalesOrder"
                          >{{site.legalCompany.naf}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"site.legalCompany.naf","key":"site.legalCompany.naf","labelKey":"NAF","labelPath":"Sales site > Legal company > NAF"},"value2":null,"key":"1","operator":"empty"}]'
                  >
                    <!--{{#if ( or ( eq site.legalCompany.naf null ) ( eq site.legalCompany.naf "" ) ( eq site.legalCompany.naf undefined ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="site.legalCompany.name"
                            data-property-data-format=""
                            data-property-parent-context="SalesOrder"
                            >{{site.legalCompany.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Address line 1"
                        data-property-data-type="String"
                        data-property-name="site.legalCompany.primaryAddress.addressLine1"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{site.legalCompany.primaryAddress.addressLine1}}</span
                      ><span
                        class="property"
                        data-property-display-label="Address line 2"
                        data-property-data-type="String"
                        data-property-name="site.legalCompany.primaryAddress.addressLine2"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{site.legalCompany.primaryAddress.addressLine2}}</span
                      ><span
                        class="property"
                        data-property-display-label="City"
                        data-property-data-type="String"
                        data-property-name="site.legalCompany.primaryAddress.city"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{site.legalCompany.primaryAddress.city}}</span
                      ><span
                        class="property"
                        data-property-display-label="Region"
                        data-property-data-type="String"
                        data-property-name="site.legalCompany.primaryAddress.region"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{site.legalCompany.primaryAddress.region}}</span
                      ><span
                        class="property"
                        data-property-display-label="Postal code"
                        data-property-data-type="String"
                        data-property-name="site.legalCompany.primaryAddress.postcode"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{site.legalCompany.primaryAddress.postcode}}</span
                      ><span
                        class="property"
                        data-property-display-label="Name"
                        data-property-data-type="String"
                        data-property-name="site.legalCompany.primaryAddress.country.name"
                        data-property-data-format=""
                        data-property-parent-context="SalesOrder"
                        >{{site.legalCompany.primaryAddress.country.name}}</span
                      ></span
                    >
                  </p>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
      </div>
    </section>
    <section
      class="conditional-block"
      data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"External note","data":{"name":"isExternalNote","title":"External note","canSort":true,"canFilter":true,"type":"Boolean","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"External note","node":"Boolean","iconType":"csv"},"id":"isExternalNote","key":"isExternalNote","labelKey":"External note","labelPath":"External note"},"value2":"true","key":"1","operator":"equals"}]'
    >
      <!--{{#if ( eq isExternalNote true )}}-->
      <div class="conditional-block-body">
        <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
          ><strong>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</strong
          ><span
            class="property"
            data-property-display-label="External note"
            data-property-data-type="Boolean"
            data-property-name="isExternalNote"
            data-property-data-format=""
            data-property-parent-context="SalesOrder"
            >{{isExternalNote}}</span
          ></span
        >
      </div>
      <!--{{/if}}-->
      <div class="conditional-block-footer">&nbsp;</div>
    </section>
  </div>
  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
</section>
<p>&nbsp;</p>
