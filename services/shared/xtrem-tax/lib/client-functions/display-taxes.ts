import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import type * as interfaces from './interfaces';

export function refreshTaxCalculationStatus(lines: ui.fields.Table, taxCalculationStatus: ui.fields.Label) {
    if (lines.value.length === 0) {
        taxCalculationStatus.value = 'notDone';
        taxCalculationStatus.isHidden = false;
    } else if (lines.value.length && lines.value.some(line => line.taxCalculationStatus === 'failed')) {
        taxCalculationStatus.value = 'failed';
        taxCalculationStatus.isHidden = false;
    } else if (lines.value.length && lines.value.every(line => line.taxCalculationStatus === 'done')) {
        taxCalculationStatus.value = 'done';
        taxCalculationStatus.isHidden = true;
    } else {
        taxCalculationStatus.value = 'notDone';
        taxCalculationStatus.isHidden = false;
    }
}

/**
 * Gets you into the tax panel,
 * it handles the tax per document line which concerns: the opening of the panel
 * @param pageInstance the calling page's this (used to interact with the framework)
 * @param taxes a string containing stringified taxes collection
 * @returns Promise
 */
export async function displayTaxes(
    pageInstance: ui.Page & {
        lines?: ui.fields.Table | ui.fields.NestedGrid;
        taxAmount?: ui.fields.Numeric;
        lineAmountIncludingTax?: ui.fields.Numeric;
        validate?: ui.PageAction;
        taxLineCalculationStatus?: ui.fields.Text;
        taxCalculationStatus?: ui.fields.Label;
    },
    rowItem: { uiTaxes?: string; _id?: string | number | boolean },
    data: interfaces.DisplayTaxesData,
    additionalLogic?: (result: any) => Promise<void>,
): Promise<void | any> {
    const result = await utils.catchPanelCrossQuitButtonAsNoop(
        pageInstance.$.dialog.page(
            '@sage/xtrem-tax/TaxPanel',
            {
                uiTaxes: rowItem.uiTaxes ?? '{taxEngine:"", taxes:[]}',
                data: JSON.stringify(data),
            },
            {
                size: 'extra-large',
            },
        ),
    );
    if (result) {
        if (additionalLogic) {
            await additionalLogic(result);
        }

        if (pageInstance.lines && pageInstance.lines instanceof ui.fields.Table) {
            pageInstance.lines.addOrUpdateRecordValue({
                _id: rowItem._id,
                uiTaxes: JSON.stringify(result.uiTaxes),
                taxAmount: Number(result.taxAmount),
                taxAmountAdjusted: Number(result.taxAmountAdjusted),
                lineAmountIncludingTax: Number(result.lineAmountIncludingTax),
                taxCalculationStatus: result.taxCalculationStatus,
            });
            if (pageInstance.taxCalculationStatus) {
                refreshTaxCalculationStatus(pageInstance.lines, pageInstance.taxCalculationStatus);
            }
        }
        if (pageInstance.taxAmount && pageInstance.lineAmountIncludingTax) {
            pageInstance.taxAmount.value = Number(result.taxAmount);
            pageInstance.lineAmountIncludingTax.value = Number(result.lineAmountIncludingTax);
        }

        if (pageInstance.taxLineCalculationStatus) {
            pageInstance.taxLineCalculationStatus.value = result.taxCalculationStatus;
        }

        if (pageInstance.validate) {
            pageInstance.validate.isDisabled = false;
        }

        rowItem.uiTaxes = JSON.stringify(result.uiTaxes);
    }
    return result;
}

export function recalculateTaxCalculationStatus(lines: ui.fields.Table, taxCalculationStatus: ui.fields.Label) {
    if (lines.value.length === 0) {
        taxCalculationStatus.value = 'notDone';
    } else if (lines.value.length && lines.value.some(line => line.taxCalculationStatus === 'failed')) {
        taxCalculationStatus.value = 'failed';
    } else if (lines.value.length && lines.value.every(line => line.taxCalculationStatus === 'done')) {
        taxCalculationStatus.value = 'done';
    } else {
        taxCalculationStatus.value = 'notDone';
    }
}

/**
 * Gets you into the base tax panel,
 * it shows
 * @param options.pageInstance the calling page's this (used to interact with the framework)
 * @param data a base tax data object
 */
export async function displayBaseTax(options: { pageInstance: ui.Page; data: interfaces.DisplayBaseTaxData }) {
    await utils.catchPanelCrossQuitButtonAsNoop(
        options.pageInstance.$.dialog.page(
            '@sage/xtrem-tax/BaseTaxPanel',
            {
                data: JSON.stringify(options.data),
            },
            {
                size: 'extra-large',
            },
        ),
    );
}
